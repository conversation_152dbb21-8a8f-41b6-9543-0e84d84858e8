BL51 BANKED LINKER/LOCATER V6.22                                                        07/07/2025  17:16:11  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\adc3085.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\rtc3085.obj, .\O
>> bjects\uart3085.obj, .\Objects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\adc3085.obj (ADC3085)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\rtc3085.obj (RTC3085)
  .\Objects\uart3085.obj (UART3085)
  .\Objects\main.obj (MAIN)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (ADC3085)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    000EH     0006H     UNIT         ?DT?RTC3085
            DATA    0014H     0004H     UNIT         ?DT?MAIN
            DATA    0018H     0004H     UNIT         _DATA_GROUP_
            DATA    001CH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   001EH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     097FH     UNIT         ?CO?LCD9648
            CODE    098DH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0B6FH     0167H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0CD6H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0D99H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    0E45H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    0ED3H     008CH     UNIT         ?C_C51STARTUP
            CODE    0F5FH     0086H     UNIT         ?CO?MAIN
            CODE    0FE5H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    1056H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    10BEH     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    1108H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    1143H     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    1178H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 2


            CODE    11ACH     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    11D9H     0029H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    1202H     0024H     UNIT         ?PR?SPI_READ?ADC3085
            CODE    1226H     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    1249H     001CH     UNIT         ?PR?_SPI_WRITE?ADC3085
            CODE    1265H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1280H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    129BH     001BH     UNIT         ?PR?MAIN?MAIN
            CODE    12B6H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    12D0H     0019H     UNIT         ?PR?_READ_AD_DATA?ADC3085
            CODE    12E9H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1302H     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    1318H     0016H     UNIT         ?PR?INITUART?UART3085
            CODE    132EH     0013H     UNIT         ?C_INITSEG
            CODE    1341H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1354H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1366H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1375H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1383H     000CH     UNIT         ?CO?RTC3085
            CODE    138FH     000BH     UNIT         ?PR?SPI_START?ADC3085
            CODE    139AH     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (ADC3085)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 3



?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0018H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0018H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648



SYMBOL TABLE OF MODULE:  .\Objects\Project (ADC3085)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ADC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  B:00A0H.5       PUBLIC        XPT2046_DIN
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:12D0H         PUBLIC        _Read_AD_Data
  B:00A0H.6       PUBLIC        XPT2046_CS
  C:1202H         PUBLIC        SPI_Read
  B:00A0H.7       PUBLIC        XPT2046_SCLK
  C:1249H         PUBLIC        _SPI_Write
  C:138FH         PUBLIC        SPI_Start
  D:00C8H         PUBLIC        T2CON
  B:00A0H.4       PUBLIC        XPT2046_DOUT
  D:00D0H         PUBLIC        PSW
  -------         PROC          SPI_START
  C:138FH         LINE#         9
  C:138FH         LINE#         10
  C:138FH         LINE#         11
  C:1391H         LINE#         12
  C:1393H         LINE#         13
  C:1395H         LINE#         14
  C:1397H         LINE#         15
  C:1399H         LINE#         16
  -------         ENDPROC       SPI_START
  -------         PROC          _SPI_WRITE
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1249H         LINE#         24
  C:1249H         LINE#         25
  C:1249H         LINE#         27
  C:124BH         LINE#         28
  C:124DH         LINE#         29
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 4


  C:124DH         LINE#         30
  C:1258H         LINE#         31
  C:125CH         LINE#         32
  C:125EH         LINE#         34
  C:1260H         LINE#         36
  C:1264H         LINE#         37
  -------         ENDPROC       _SPI_WRITE
  -------         PROC          SPI_READ
  -------         DO            
  D:0004H         SYMBOL        i
  D:0006H         SYMBOL        dat
  -------         ENDDO         
  C:1202H         LINE#         45
  C:1202H         LINE#         46
  C:1202H         LINE#         47
  C:1205H         LINE#         48
  C:1207H         LINE#         49
  C:1209H         LINE#         50
  C:1209H         LINE#         51
  C:1210H         LINE#         53
  C:1212H         LINE#         54
  C:1214H         LINE#         56
  C:121AH         LINE#         58
  C:1225H         LINE#         59
  C:1225H         LINE#         60
  -------         ENDPROC       SPI_READ
  -------         PROC          _READ_AD_DATA
  D:0007H         SYMBOL        cmd
  -------         DO            
  D:0007H         SYMBOL        i
  D:0006H         SYMBOL        AD_Value
  -------         ENDDO         
  C:12D0H         LINE#         68
  C:12D0H         LINE#         69
  C:12D0H         LINE#         72
  C:12D2H         LINE#         73
  C:12D4H         LINE#         74
  C:12D7H         LINE#         75
  C:12DBH         LINE#         76
  C:12DDH         LINE#         77
  C:12DEH         LINE#         78
  C:12DFH         LINE#         79
  C:12E1H         LINE#         80
  C:12E2H         LINE#         81
  C:12E3H         LINE#         82
  C:12E6H         LINE#         83
  C:12E8H         LINE#         84
  C:12E8H         LINE#         85
  -------         ENDPROC       _READ_AD_DATA
  -------         ENDMOD        ADC3085

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1375H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 5


  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1265H         PUBLIC        ds18b20_read_byte
  C:139AH         PUBLIC        ds18b20_init
  C:12B6H         PUBLIC        ds18b20_read_bit
  C:1180H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:0FE5H         PUBLIC        ds18b20_read_temperture
  C:1366H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:1108H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1366H         LINE#         4
  C:1366H         LINE#         5
  C:1366H         LINE#         7
  C:136CH         LINE#         8
  C:136CH         LINE#         9
  C:136EH         LINE#         10
  C:1374H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1375H         LINE#         21
  C:1375H         LINE#         22
  C:1375H         LINE#         23
  C:1377H         LINE#         24
  C:137CH         LINE#         25
  C:137EH         LINE#         26
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:1108H         LINE#         35
  C:1108H         LINE#         36
  C:1108H         LINE#         37
  C:110AH         LINE#         39
  C:1113H         LINE#         40
  C:1113H         LINE#         41
  C:1114H         LINE#         42
  C:1119H         LINE#         43
  C:111BH         LINE#         44
  C:1124H         LINE#         45
  C:1126H         LINE#         46
  C:112FH         LINE#         47
  C:112FH         LINE#         48
  C:1130H         LINE#         49
  C:1135H         LINE#         50
  C:1137H         LINE#         51
  C:1140H         LINE#         52
  C:1142H         LINE#         53
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 6


  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:12B6H         LINE#         61
  C:12B6H         LINE#         62
  C:12B6H         LINE#         63
  C:12B8H         LINE#         65
  C:12BAH         LINE#         66
  C:12BCH         LINE#         67
  C:12BEH         LINE#         68
  C:12C0H         LINE#         69
  C:12C6H         LINE#         70
  C:12C8H         LINE#         71
  C:12CDH         LINE#         72
  C:12CFH         LINE#         73
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1265H         LINE#         81
  C:1265H         LINE#         82
  C:1265H         LINE#         83
  C:1267H         LINE#         84
  C:1268H         LINE#         85
  C:1269H         LINE#         87
  C:1269H         LINE#         88
  C:1269H         LINE#         89
  C:126CH         LINE#         90
  C:1279H         LINE#         91
  C:127DH         LINE#         92
  C:127FH         LINE#         93
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1180H         LINE#         101
  C:1182H         LINE#         102
  C:1182H         LINE#         103
  C:1184H         LINE#         104
  C:1184H         LINE#         106
  C:1184H         LINE#         107
  C:1184H         LINE#         108
  C:1188H         LINE#         109
  C:118CH         LINE#         110
  C:118FH         LINE#         111
  C:118FH         LINE#         112
  C:1191H         LINE#         113
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 7


  C:1193H         LINE#         114
  C:1195H         LINE#         115
  C:119AH         LINE#         116
  C:119CH         LINE#         118
  C:119CH         LINE#         119
  C:119EH         LINE#         120
  C:11A3H         LINE#         121
  C:11A5H         LINE#         122
  C:11A7H         LINE#         123
  C:11A7H         LINE#         124
  C:11ABH         LINE#         125
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0003H         LINE#         137
  C:0006H         LINE#         138
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:139AH         LINE#         147
  C:139AH         LINE#         148
  C:139AH         LINE#         149
  C:139DH         LINE#         150
  C:13A0H         LINE#         151
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0008H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:000CH         SYMBOL        value
  -------         ENDDO         
  C:0FE5H         LINE#         159
  C:0FE5H         LINE#         160
  C:0FE5H         LINE#         162
  C:0FE7H         LINE#         163
  C:0FE8H         LINE#         164
  C:0FECH         LINE#         166
  C:0FEFH         LINE#         167
  C:0FEFH         LINE#         168
  C:0FEFH         LINE#         169
  C:0FF2H         LINE#         170
  C:0FF7H         LINE#         172
  C:0FFCH         LINE#         173
  C:0FFFH         LINE#         174
  C:100BH         LINE#         176
  C:1012H         LINE#         177
  C:1012H         LINE#         178
  C:1023H         LINE#         179
  C:1031H         LINE#         180
  C:1033H         LINE#         182
  C:1033H         LINE#         183
  C:104DH         LINE#         184
  C:104DH         LINE#         185
  C:1055H         LINE#         186
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 8


  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1291H         PUBLIC        _WriteData
  C:0E45H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:135CH         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:1056H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:033EH         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0CD6H         PUBLIC        _LCD9648_Write16CnCHAR
  C:0D99H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:12E9H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:11ACH         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:12E9H         LINE#         4
  C:12E9H         LINE#         5
  C:12E9H         LINE#         8
  C:12EBH         LINE#         9
  C:12EBH         LINE#         10
  C:12F3H         LINE#         11
  C:12F5H         LINE#         13
  C:12F9H         LINE#         15
  C:12FBH         LINE#         16
  C:12FDH         LINE#         17
  C:1301H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1356H         SYMBOL        L?0067
  C:1358H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1356H         SYMBOL        L?0067
  C:1358H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:135CH         LINE#         20
  C:135CH         LINE#         21
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 9


  C:135CH         LINE#         23
  C:135EH         LINE#         24
  C:1360H         LINE#         26
  C:1363H         LINE#         28
  C:1365H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:1285H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1285H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1291H         LINE#         31
  C:1291H         LINE#         32
  C:1291H         LINE#         33
  C:1293H         LINE#         34
  C:1295H         LINE#         36
  C:1298H         LINE#         38
  C:129AH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1056H         LINE#         41
  C:1056H         LINE#         42
  C:1056H         LINE#         46
  C:1058H         LINE#         47
  C:1066H         LINE#         49
  C:1068H         LINE#         50
  C:1076H         LINE#         52
  C:1078H         LINE#         53
  C:1086H         LINE#         55
  C:108DH         LINE#         56
  C:1094H         LINE#         57
  C:109BH         LINE#         58
  C:10A2H         LINE#         59
  C:10A9H         LINE#         60
  C:10B0H         LINE#         61
  C:10B7H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:11ACH         LINE#         67
  C:11ACH         LINE#         68
  C:11ACH         LINE#         71
  C:11AEH         LINE#         72
  C:11AEH         LINE#         73
  C:11B5H         LINE#         74
  C:11BBH         LINE#         75
  C:11C2H         LINE#         76
  C:11C8H         LINE#         78
  C:11CAH         LINE#         79
  C:11CAH         LINE#         80
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 10


  C:11D0H         LINE#         81
  C:11D4H         LINE#         82
  C:11D8H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0018H         SYMBOL        x
  D:0019H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:001AH         SYMBOL        x1
  D:001BH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0CD6H         LINE#         86
  C:0CDAH         LINE#         87
  C:0CDAH         LINE#         91
  C:0CE1H         LINE#         92
  C:0CE1H         LINE#         93
  C:0CE4H         LINE#         94
  C:0CE4H         LINE#         97
  C:0CEBH         LINE#         98
  C:0CEBH         LINE#         99
  C:0CEEH         LINE#         100
  C:0CEEH         LINE#         101
  C:0CF4H         LINE#         103
  C:0CFAH         LINE#         104
  C:0D02H         LINE#         105
  C:0D02H         LINE#         108
  C:0D09H         LINE#         110
  C:0D10H         LINE#         111
  C:0D16H         LINE#         113
  C:0D19H         LINE#         114
  C:0D20H         LINE#         115
  C:0D22H         LINE#         116
  C:0D22H         LINE#         118
  C:0D55H         LINE#         120
  C:0D55H         LINE#         121
  C:0D56H         LINE#         122
  C:0D56H         LINE#         123
  C:0D5BH         LINE#         124
  C:0D5BH         LINE#         126
  C:0D62H         LINE#         129
  C:0D65H         LINE#         130
  C:0D6CH         LINE#         131
  C:0D6CH         LINE#         132
  C:0D7CH         LINE#         133
  C:0D80H         LINE#         134
  C:0D86H         LINE#         135
  C:0D86H         LINE#         136
  C:0D8CH         LINE#         137
  C:0D93H         LINE#         139
  C:0D96H         LINE#         140
  C:0D98H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0018H         SYMBOL        x
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 11


  D:0019H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:001AH         SYMBOL        x1
  D:001BH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0D99H         LINE#         143
  C:0D9DH         LINE#         144
  C:0D9DH         LINE#         148
  C:0DA4H         LINE#         149
  C:0DA4H         LINE#         150
  C:0DA7H         LINE#         151
  C:0DA7H         LINE#         154
  C:0DAEH         LINE#         155
  C:0DAEH         LINE#         156
  C:0DB1H         LINE#         157
  C:0DB1H         LINE#         158
  C:0DB7H         LINE#         160
  C:0DBDH         LINE#         161
  C:0DC5H         LINE#         162
  C:0DC5H         LINE#         165
  C:0DCCH         LINE#         167
  C:0DD3H         LINE#         168
  C:0DD9H         LINE#         170
  C:0DDEH         LINE#         171
  C:0DE5H         LINE#         172
  C:0DE7H         LINE#         173
  C:0DE7H         LINE#         175
  C:0DFFH         LINE#         176
  C:0DFFH         LINE#         177
  C:0E00H         LINE#         178
  C:0E00H         LINE#         179
  C:0E05H         LINE#         180
  C:0E05H         LINE#         182
  C:0E0CH         LINE#         185
  C:0E11H         LINE#         186
  C:0E18H         LINE#         187
  C:0E18H         LINE#         188
  C:0E28H         LINE#         189
  C:0E2CH         LINE#         190
  C:0E32H         LINE#         191
  C:0E32H         LINE#         192
  C:0E38H         LINE#         193
  C:0E3FH         LINE#         195
  C:0E42H         LINE#         196
  C:0E44H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:001CH         SYMBOL        x1
  D:001DH         SYMBOL        x2
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 12


  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E45H         LINE#         198
  C:0E47H         LINE#         199
  C:0E47H         LINE#         203
  C:0E4DH         LINE#         204
  C:0E4DH         LINE#         205
  C:0E50H         LINE#         206
  C:0E50H         LINE#         209
  C:0E56H         LINE#         210
  C:0E56H         LINE#         211
  C:0E59H         LINE#         212
  C:0E59H         LINE#         213
  C:0E5DH         LINE#         215
  C:0E63H         LINE#         217
  C:0E63H         LINE#         220
  C:0E6AH         LINE#         222
  C:0E70H         LINE#         223
  C:0E75H         LINE#         225
  C:0E7AH         LINE#         226
  C:0E81H         LINE#         227
  C:0E83H         LINE#         228
  C:0E83H         LINE#         230
  C:0E97H         LINE#         231
  C:0E97H         LINE#         232
  C:0E98H         LINE#         233
  C:0E98H         LINE#         234
  C:0E9DH         LINE#         235
  C:0E9DH         LINE#         237
  C:0EA3H         LINE#         240
  C:0EA8H         LINE#         241
  C:0EAFH         LINE#         242
  C:0EAFH         LINE#         243
  C:0EC4H         LINE#         244
  C:0EC8H         LINE#         245
  C:0ECCH         LINE#         246
  C:0ECCH         LINE#         247
  C:0ED0H         LINE#         248
  C:0ED0H         LINE#         250
  C:0ED0H         LINE#         251
  C:0ED2H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  C:1143H         PUBLIC        _Ds1302Write
  C:1302H         PUBLIC        Ds1302ReadTime
  C:1226H         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:000EH         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 13


  D:0088H         PUBLIC        TCON
  C:1389H         PUBLIC        WRITE_RTC_ADDR
  C:1383H         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:10BEH         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:1143H         LINE#         24
  C:1143H         LINE#         25
  C:1143H         LINE#         27
  C:1145H         LINE#         28
  C:1146H         LINE#         30
  C:1148H         LINE#         31
  C:1149H         LINE#         32
  C:114BH         LINE#         33
  C:114CH         LINE#         35
  C:114EH         LINE#         36
  C:114EH         LINE#         37
  C:1152H         LINE#         38
  C:1156H         LINE#         39
  C:1158H         LINE#         40
  C:1159H         LINE#         41
  C:115BH         LINE#         42
  C:115CH         LINE#         43
  C:1160H         LINE#         44
  C:1162H         LINE#         45
  C:1162H         LINE#         46
  C:1166H         LINE#         47
  C:116AH         LINE#         48
  C:116CH         LINE#         49
  C:116DH         LINE#         50
  C:116FH         LINE#         51
  C:1170H         LINE#         52
  C:1174H         LINE#         54
  C:1176H         LINE#         55
  C:1177H         LINE#         56
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
  -------         ENDDO         
  C:10BEH         LINE#         65
  C:10BEH         LINE#         66
  C:10BEH         LINE#         68
  C:10C0H         LINE#         69
  C:10C1H         LINE#         71
  C:10C3H         LINE#         72
  C:10C4H         LINE#         73
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 14


  C:10C6H         LINE#         74
  C:10C7H         LINE#         76
  C:10C9H         LINE#         77
  C:10C9H         LINE#         78
  C:10CDH         LINE#         79
  C:10D1H         LINE#         80
  C:10D3H         LINE#         81
  C:10D4H         LINE#         82
  C:10D6H         LINE#         83
  C:10D7H         LINE#         84
  C:10DBH         LINE#         85
  C:10DCH         LINE#         86
  C:10DEH         LINE#         87
  C:10DEH         LINE#         88
  C:10E3H         LINE#         89
  C:10EFH         LINE#         90
  C:10F1H         LINE#         91
  C:10F2H         LINE#         92
  C:10F4H         LINE#         93
  C:10F5H         LINE#         94
  C:10F9H         LINE#         96
  C:10FBH         LINE#         97
  C:10FCH         LINE#         98
  C:10FEH         LINE#         99
  C:10FFH         LINE#         100
  C:1101H         LINE#         101
  C:1102H         LINE#         102
  C:1104H         LINE#         103
  C:1105H         LINE#         104
  C:1107H         LINE#         105
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:1226H         LINE#         114
  C:1226H         LINE#         115
  C:1226H         LINE#         117
  C:122DH         LINE#         118
  C:122FH         LINE#         119
  C:122FH         LINE#         120
  C:123EH         LINE#         121
  C:1242H         LINE#         122
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:1302H         LINE#         132
  C:1302H         LINE#         133
  C:1302H         LINE#         135
  C:1304H         LINE#         136
  C:1304H         LINE#         137
  C:1313H         LINE#         138
  C:1317H         LINE#         140
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 15



  -------         MODULE        UART3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  B:00A8H.4       PUBLIC        ES
  D:00B8H         PUBLIC        IP
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  C:1318H         PUBLIC        InitUart
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          INITUART
  C:1318H         LINE#         6
  C:1318H         LINE#         7
  C:1318H         LINE#         8
  C:131BH         LINE#         9
  C:1321H         LINE#         10
  C:1324H         LINE#         11
  C:1327H         LINE#         12
  C:132DH         LINE#         13
  -------         ENDPROC       INITUART
  -------         ENDMOD        UART3085

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:11D9H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:0B6FH         PUBLIC        LCD9648_Init_Proc
  C:129BH         PUBLIC        main
  C:1341H         PUBLIC        Timer0_Init
  D:0014H         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:0015H         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  D:008CH         PUBLIC        TH0
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:00C8H         PUBLIC        T2CON
  D:0017H         PUBLIC        Time_Flag
  D:00D0H         PUBLIC        PSW
  -------         PROC          TIMER0_INIT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 16


  C:1341H         LINE#         14
  C:1341H         LINE#         15
  C:1341H         LINE#         16
  C:1344H         LINE#         17
  C:1347H         LINE#         18
  C:134AH         LINE#         19
  C:134DH         LINE#         20
  C:134FH         LINE#         21
  C:1351H         LINE#         22
  C:1353H         LINE#         23
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:11D9H         LINE#         29
  C:11DDH         LINE#         31
  C:11E0H         LINE#         32
  C:11E3H         LINE#         34
  C:11EBH         LINE#         36
  C:11F4H         LINE#         37
  C:11F4H         LINE#         38
  C:11F7H         LINE#         39
  C:11FDH         LINE#         41
  C:11FDH         LINE#         42
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0B6FH         LINE#         43
  C:0B6FH         LINE#         44
  C:0B6FH         LINE#         45
  C:0B82H         LINE#         46
  C:0B82H         LINE#         47
  C:0B82H         LINE#         48
  C:0B85H         LINE#         49
  C:0B88H         LINE#         51
  C:0B94H         LINE#         52
  C:0BA1H         LINE#         54
  C:0BAEH         LINE#         55
  C:0BBBH         LINE#         56
  C:0BC8H         LINE#         57
  C:0BD5H         LINE#         58
  C:0BDFH         LINE#         59
  C:0BE2H         LINE#         61
  C:0BE2H         LINE#         62
  C:0BE5H         LINE#         63
  C:0BE8H         LINE#         65
  C:0BF4H         LINE#         66
  C:0C01H         LINE#         67
  C:0C0EH         LINE#         68
  C:0C1BH         LINE#         69
  C:0C28H         LINE#         70
  C:0C35H         LINE#         72
  C:0C42H         LINE#         73
  C:0C4FH         LINE#         74
  C:0C5CH         LINE#         75
  C:0C69H         LINE#         76
  C:0C76H         LINE#         77
  C:0C83H         LINE#         79
  C:0C90H         LINE#         80
  C:0C9DH         LINE#         81
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 17


  C:0CAAH         LINE#         82
  C:0CB4H         LINE#         83
  C:0CB6H         LINE#         85
  C:0CB6H         LINE#         86
  C:0CB9H         LINE#         87
  C:0CBCH         LINE#         88
  C:0CC8H         LINE#         89
  C:0CD5H         LINE#         90
  C:0CD5H         LINE#         92
  C:0CD5H         LINE#         94
  C:0CD5H         LINE#         95
  C:0CD5H         LINE#         99
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          MAIN
  C:129BH         LINE#         101
  C:129BH         LINE#         102
  C:129BH         LINE#         103
  C:129EH         LINE#         104
  C:12A1H         LINE#         106
  C:12A1H         LINE#         107
  C:12A1H         LINE#         108
  C:12A5H         LINE#         109
  C:12A5H         LINE#         110
  C:12A8H         LINE#         111
  C:12AAH         LINE#         112
  C:12B1H         LINE#         113
  C:12B1H         LINE#         114
  C:12B4H         LINE#         115
  C:12B4H         LINE#         116
  C:12B4H         LINE#         117
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        ?C?FPMUL
  C:098DH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:0A9DH         PUBLIC        ?C?FCASTC
  C:0A98H         PUBLIC        ?C?FCASTI
  C:0A93H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0B1BH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0B34H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0B61H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?SPI_START?ADC3085
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:16:11  PAGE 18



*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_READ_AD_DATA?ADC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS1302INIT?RTC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS1302READTIME?RTC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?INITUART?UART3085

Program Size: data=31.0 xdata=0 code=5025
LINK/LOCATE RUN COMPLETE.  8 WARNING(S),  0 ERROR(S)
