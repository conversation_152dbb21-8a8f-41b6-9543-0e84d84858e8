BL51 BANKED LINKER/LOCATER V6.22                                                        07/07/2025  16:52:48  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\adc3085.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\rtc3085.obj, .\O
>> bjects\uart3085.obj, .\Objects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\adc3085.obj (ADC3085)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\rtc3085.obj (RTC3085)
  .\Objects\uart3085.obj (UART3085)
  .\Objects\main.obj (MAIN)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (ADC3085)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0016H     UNIT         ?DT?MAIN
            DATA    001EH     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    0024H     0006H     UNIT         ?DT?RTC3085
            DATA    002AH     0004H     UNIT         _DATA_GROUP_
            DATA    002EH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   0030H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     097FH     UNIT         ?CO?LCD9648
            CODE    098DH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0B6FH     0142H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0CB1H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0D74H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    0E20H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    0EAEH     008CH     UNIT         ?C_C51STARTUP
            CODE    0F3AH     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    0FABH     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    1013H     0060H     UNIT         ?CO?MAIN
            CODE    1073H     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    10BDH     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    10F8H     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    112DH     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 2


            CODE    1161H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    118EH     0029H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    11B7H     0024H     UNIT         ?PR?SPI_READ?ADC3085
            CODE    11DBH     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    11FEH     001CH     UNIT         ?PR?_SPI_WRITE?ADC3085
            CODE    121AH     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1235H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    1250H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    126AH     0019H     UNIT         ?PR?_READ_AD_DATA?ADC3085
            CODE    1283H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    129CH     0019H     UNIT         ?C_INITSEG
            CODE    12B5H     0019H     UNIT         ?PR?MAIN?MAIN
            CODE    12CEH     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    12E4H     0016H     UNIT         ?PR?INITUART?UART3085
            CODE    12FAH     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    130DH     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    131FH     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    132EH     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    133CH     000CH     UNIT         ?CO?RTC3085
            CODE    1348H     000BH     UNIT         ?PR?SPI_START?ADC3085
            CODE    1353H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (ADC3085)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 3



?PR?_LCD9648_WRITE16CNCHAR?LCD9648          002AH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          002AH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648



SYMBOL TABLE OF MODULE:  .\Objects\Project (ADC3085)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ADC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  B:00A0H.5       PUBLIC        XPT2046_DIN
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:126AH         PUBLIC        _Read_AD_Data
  B:00A0H.6       PUBLIC        XPT2046_CS
  C:11B7H         PUBLIC        SPI_Read
  B:00A0H.7       PUBLIC        XPT2046_SCLK
  C:11FEH         PUBLIC        _SPI_Write
  C:1348H         PUBLIC        SPI_Start
  D:00C8H         PUBLIC        T2CON
  B:00A0H.4       PUBLIC        XPT2046_DOUT
  D:00D0H         PUBLIC        PSW
  -------         PROC          SPI_START
  C:1348H         LINE#         9
  C:1348H         LINE#         10
  C:1348H         LINE#         11
  C:134AH         LINE#         12
  C:134CH         LINE#         13
  C:134EH         LINE#         14
  C:1350H         LINE#         15
  C:1352H         LINE#         16
  -------         ENDPROC       SPI_START
  -------         PROC          _SPI_WRITE
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:11FEH         LINE#         24
  C:11FEH         LINE#         25
  C:11FEH         LINE#         27
  C:1200H         LINE#         28
  C:1202H         LINE#         29
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 4


  C:1202H         LINE#         30
  C:120DH         LINE#         31
  C:1211H         LINE#         32
  C:1213H         LINE#         34
  C:1215H         LINE#         36
  C:1219H         LINE#         37
  -------         ENDPROC       _SPI_WRITE
  -------         PROC          SPI_READ
  -------         DO            
  D:0004H         SYMBOL        i
  D:0006H         SYMBOL        dat
  -------         ENDDO         
  C:11B7H         LINE#         45
  C:11B7H         LINE#         46
  C:11B7H         LINE#         47
  C:11BAH         LINE#         48
  C:11BCH         LINE#         49
  C:11BEH         LINE#         50
  C:11BEH         LINE#         51
  C:11C5H         LINE#         53
  C:11C7H         LINE#         54
  C:11C9H         LINE#         56
  C:11CFH         LINE#         58
  C:11DAH         LINE#         59
  C:11DAH         LINE#         60
  -------         ENDPROC       SPI_READ
  -------         PROC          _READ_AD_DATA
  D:0007H         SYMBOL        cmd
  -------         DO            
  D:0007H         SYMBOL        i
  D:0006H         SYMBOL        AD_Value
  -------         ENDDO         
  C:126AH         LINE#         68
  C:126AH         LINE#         69
  C:126AH         LINE#         72
  C:126CH         LINE#         73
  C:126EH         LINE#         74
  C:1271H         LINE#         75
  C:1275H         LINE#         76
  C:1277H         LINE#         77
  C:1278H         LINE#         78
  C:1279H         LINE#         79
  C:127BH         LINE#         80
  C:127CH         LINE#         81
  C:127DH         LINE#         82
  C:1280H         LINE#         83
  C:1282H         LINE#         84
  C:1282H         LINE#         85
  -------         ENDPROC       _READ_AD_DATA
  -------         ENDMOD        ADC3085

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:132EH         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 5


  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:121AH         PUBLIC        ds18b20_read_byte
  C:1353H         PUBLIC        ds18b20_init
  C:1250H         PUBLIC        ds18b20_read_bit
  C:1135H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:0F3AH         PUBLIC        ds18b20_read_temperture
  C:131FH         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:10BDH         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:131FH         LINE#         4
  C:131FH         LINE#         5
  C:131FH         LINE#         7
  C:1325H         LINE#         8
  C:1325H         LINE#         9
  C:1327H         LINE#         10
  C:132DH         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:132EH         LINE#         21
  C:132EH         LINE#         22
  C:132EH         LINE#         23
  C:1330H         LINE#         24
  C:1335H         LINE#         25
  C:1337H         LINE#         26
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:10BDH         LINE#         35
  C:10BDH         LINE#         36
  C:10BDH         LINE#         37
  C:10BFH         LINE#         39
  C:10C8H         LINE#         40
  C:10C8H         LINE#         41
  C:10C9H         LINE#         42
  C:10CEH         LINE#         43
  C:10D0H         LINE#         44
  C:10D9H         LINE#         45
  C:10DBH         LINE#         46
  C:10E4H         LINE#         47
  C:10E4H         LINE#         48
  C:10E5H         LINE#         49
  C:10EAH         LINE#         50
  C:10ECH         LINE#         51
  C:10F5H         LINE#         52
  C:10F7H         LINE#         53
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 6


  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:1250H         LINE#         61
  C:1250H         LINE#         62
  C:1250H         LINE#         63
  C:1252H         LINE#         65
  C:1254H         LINE#         66
  C:1256H         LINE#         67
  C:1258H         LINE#         68
  C:125AH         LINE#         69
  C:1260H         LINE#         70
  C:1262H         LINE#         71
  C:1267H         LINE#         72
  C:1269H         LINE#         73
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:121AH         LINE#         81
  C:121AH         LINE#         82
  C:121AH         LINE#         83
  C:121CH         LINE#         84
  C:121DH         LINE#         85
  C:121EH         LINE#         87
  C:121EH         LINE#         88
  C:121EH         LINE#         89
  C:1221H         LINE#         90
  C:122EH         LINE#         91
  C:1232H         LINE#         92
  C:1234H         LINE#         93
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1135H         LINE#         101
  C:1137H         LINE#         102
  C:1137H         LINE#         103
  C:1139H         LINE#         104
  C:1139H         LINE#         106
  C:1139H         LINE#         107
  C:1139H         LINE#         108
  C:113DH         LINE#         109
  C:1141H         LINE#         110
  C:1144H         LINE#         111
  C:1144H         LINE#         112
  C:1146H         LINE#         113
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 7


  C:1148H         LINE#         114
  C:114AH         LINE#         115
  C:114FH         LINE#         116
  C:1151H         LINE#         118
  C:1151H         LINE#         119
  C:1153H         LINE#         120
  C:1158H         LINE#         121
  C:115AH         LINE#         122
  C:115CH         LINE#         123
  C:115CH         LINE#         124
  C:1160H         LINE#         125
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0003H         LINE#         137
  C:0006H         LINE#         138
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1353H         LINE#         147
  C:1353H         LINE#         148
  C:1353H         LINE#         149
  C:1356H         LINE#         150
  C:1359H         LINE#         151
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:001EH         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:0022H         SYMBOL        value
  -------         ENDDO         
  C:0F3AH         LINE#         159
  C:0F3AH         LINE#         160
  C:0F3AH         LINE#         162
  C:0F3CH         LINE#         163
  C:0F3DH         LINE#         164
  C:0F41H         LINE#         166
  C:0F44H         LINE#         167
  C:0F44H         LINE#         168
  C:0F44H         LINE#         169
  C:0F47H         LINE#         170
  C:0F4CH         LINE#         172
  C:0F51H         LINE#         173
  C:0F54H         LINE#         174
  C:0F60H         LINE#         176
  C:0F67H         LINE#         177
  C:0F67H         LINE#         178
  C:0F78H         LINE#         179
  C:0F86H         LINE#         180
  C:0F88H         LINE#         182
  C:0F88H         LINE#         183
  C:0FA2H         LINE#         184
  C:0FA2H         LINE#         185
  C:0FAAH         LINE#         186
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 8


  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1246H         PUBLIC        _WriteData
  C:0E20H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1315H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:0FABH         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:033EH         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0CB1H         PUBLIC        _LCD9648_Write16CnCHAR
  C:0D74H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1283H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:1161H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1283H         LINE#         4
  C:1283H         LINE#         5
  C:1283H         LINE#         8
  C:1285H         LINE#         9
  C:1285H         LINE#         10
  C:128DH         LINE#         11
  C:128FH         LINE#         13
  C:1293H         LINE#         15
  C:1295H         LINE#         16
  C:1297H         LINE#         17
  C:129BH         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:130FH         SYMBOL        L?0067
  C:1311H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:130FH         SYMBOL        L?0067
  C:1311H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1315H         LINE#         20
  C:1315H         LINE#         21
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 9


  C:1315H         LINE#         23
  C:1317H         LINE#         24
  C:1319H         LINE#         26
  C:131CH         LINE#         28
  C:131EH         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:123AH         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:123AH         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1246H         LINE#         31
  C:1246H         LINE#         32
  C:1246H         LINE#         33
  C:1248H         LINE#         34
  C:124AH         LINE#         36
  C:124DH         LINE#         38
  C:124FH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:0FABH         LINE#         41
  C:0FABH         LINE#         42
  C:0FABH         LINE#         46
  C:0FADH         LINE#         47
  C:0FBBH         LINE#         49
  C:0FBDH         LINE#         50
  C:0FCBH         LINE#         52
  C:0FCDH         LINE#         53
  C:0FDBH         LINE#         55
  C:0FE2H         LINE#         56
  C:0FE9H         LINE#         57
  C:0FF0H         LINE#         58
  C:0FF7H         LINE#         59
  C:0FFEH         LINE#         60
  C:1005H         LINE#         61
  C:100CH         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1161H         LINE#         67
  C:1161H         LINE#         68
  C:1161H         LINE#         71
  C:1163H         LINE#         72
  C:1163H         LINE#         73
  C:116AH         LINE#         74
  C:1170H         LINE#         75
  C:1177H         LINE#         76
  C:117DH         LINE#         78
  C:117FH         LINE#         79
  C:117FH         LINE#         80
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 10


  C:1185H         LINE#         81
  C:1189H         LINE#         82
  C:118DH         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:002AH         SYMBOL        x
  D:002BH         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:002CH         SYMBOL        x1
  D:002DH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0CB1H         LINE#         86
  C:0CB5H         LINE#         87
  C:0CB5H         LINE#         91
  C:0CBCH         LINE#         92
  C:0CBCH         LINE#         93
  C:0CBFH         LINE#         94
  C:0CBFH         LINE#         97
  C:0CC6H         LINE#         98
  C:0CC6H         LINE#         99
  C:0CC9H         LINE#         100
  C:0CC9H         LINE#         101
  C:0CCFH         LINE#         103
  C:0CD5H         LINE#         104
  C:0CDDH         LINE#         105
  C:0CDDH         LINE#         108
  C:0CE4H         LINE#         110
  C:0CEBH         LINE#         111
  C:0CF1H         LINE#         113
  C:0CF4H         LINE#         114
  C:0CFBH         LINE#         115
  C:0CFDH         LINE#         116
  C:0CFDH         LINE#         118
  C:0D30H         LINE#         120
  C:0D30H         LINE#         121
  C:0D31H         LINE#         122
  C:0D31H         LINE#         123
  C:0D36H         LINE#         124
  C:0D36H         LINE#         126
  C:0D3DH         LINE#         129
  C:0D40H         LINE#         130
  C:0D47H         LINE#         131
  C:0D47H         LINE#         132
  C:0D57H         LINE#         133
  C:0D5BH         LINE#         134
  C:0D61H         LINE#         135
  C:0D61H         LINE#         136
  C:0D67H         LINE#         137
  C:0D6EH         LINE#         139
  C:0D71H         LINE#         140
  C:0D73H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:002AH         SYMBOL        x
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 11


  D:002BH         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:002CH         SYMBOL        x1
  D:002DH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0D74H         LINE#         143
  C:0D78H         LINE#         144
  C:0D78H         LINE#         148
  C:0D7FH         LINE#         149
  C:0D7FH         LINE#         150
  C:0D82H         LINE#         151
  C:0D82H         LINE#         154
  C:0D89H         LINE#         155
  C:0D89H         LINE#         156
  C:0D8CH         LINE#         157
  C:0D8CH         LINE#         158
  C:0D92H         LINE#         160
  C:0D98H         LINE#         161
  C:0DA0H         LINE#         162
  C:0DA0H         LINE#         165
  C:0DA7H         LINE#         167
  C:0DAEH         LINE#         168
  C:0DB4H         LINE#         170
  C:0DB9H         LINE#         171
  C:0DC0H         LINE#         172
  C:0DC2H         LINE#         173
  C:0DC2H         LINE#         175
  C:0DDAH         LINE#         176
  C:0DDAH         LINE#         177
  C:0DDBH         LINE#         178
  C:0DDBH         LINE#         179
  C:0DE0H         LINE#         180
  C:0DE0H         LINE#         182
  C:0DE7H         LINE#         185
  C:0DECH         LINE#         186
  C:0DF3H         LINE#         187
  C:0DF3H         LINE#         188
  C:0E03H         LINE#         189
  C:0E07H         LINE#         190
  C:0E0DH         LINE#         191
  C:0E0DH         LINE#         192
  C:0E13H         LINE#         193
  C:0E1AH         LINE#         195
  C:0E1DH         LINE#         196
  C:0E1FH         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:002EH         SYMBOL        x1
  D:002FH         SYMBOL        x2
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 12


  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E20H         LINE#         198
  C:0E22H         LINE#         199
  C:0E22H         LINE#         203
  C:0E28H         LINE#         204
  C:0E28H         LINE#         205
  C:0E2BH         LINE#         206
  C:0E2BH         LINE#         209
  C:0E31H         LINE#         210
  C:0E31H         LINE#         211
  C:0E34H         LINE#         212
  C:0E34H         LINE#         213
  C:0E38H         LINE#         215
  C:0E3EH         LINE#         217
  C:0E3EH         LINE#         220
  C:0E45H         LINE#         222
  C:0E4BH         LINE#         223
  C:0E50H         LINE#         225
  C:0E55H         LINE#         226
  C:0E5CH         LINE#         227
  C:0E5EH         LINE#         228
  C:0E5EH         LINE#         230
  C:0E72H         LINE#         231
  C:0E72H         LINE#         232
  C:0E73H         LINE#         233
  C:0E73H         LINE#         234
  C:0E78H         LINE#         235
  C:0E78H         LINE#         237
  C:0E7EH         LINE#         240
  C:0E83H         LINE#         241
  C:0E8AH         LINE#         242
  C:0E8AH         LINE#         243
  C:0E9FH         LINE#         244
  C:0EA3H         LINE#         245
  C:0EA7H         LINE#         246
  C:0EA7H         LINE#         247
  C:0EABH         LINE#         248
  C:0EABH         LINE#         250
  C:0EABH         LINE#         251
  C:0EADH         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  C:10F8H         PUBLIC        _Ds1302Write
  C:12CEH         PUBLIC        Ds1302ReadTime
  C:11DBH         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:0024H         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 13


  D:0088H         PUBLIC        TCON
  C:1342H         PUBLIC        WRITE_RTC_ADDR
  C:133CH         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:1073H         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:10F8H         LINE#         24
  C:10F8H         LINE#         25
  C:10F8H         LINE#         27
  C:10FAH         LINE#         28
  C:10FBH         LINE#         30
  C:10FDH         LINE#         31
  C:10FEH         LINE#         32
  C:1100H         LINE#         33
  C:1101H         LINE#         35
  C:1103H         LINE#         36
  C:1103H         LINE#         37
  C:1107H         LINE#         38
  C:110BH         LINE#         39
  C:110DH         LINE#         40
  C:110EH         LINE#         41
  C:1110H         LINE#         42
  C:1111H         LINE#         43
  C:1115H         LINE#         44
  C:1117H         LINE#         45
  C:1117H         LINE#         46
  C:111BH         LINE#         47
  C:111FH         LINE#         48
  C:1121H         LINE#         49
  C:1122H         LINE#         50
  C:1124H         LINE#         51
  C:1125H         LINE#         52
  C:1129H         LINE#         54
  C:112BH         LINE#         55
  C:112CH         LINE#         56
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
  -------         ENDDO         
  C:1073H         LINE#         65
  C:1073H         LINE#         66
  C:1073H         LINE#         68
  C:1075H         LINE#         69
  C:1076H         LINE#         71
  C:1078H         LINE#         72
  C:1079H         LINE#         73
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 14


  C:107BH         LINE#         74
  C:107CH         LINE#         76
  C:107EH         LINE#         77
  C:107EH         LINE#         78
  C:1082H         LINE#         79
  C:1086H         LINE#         80
  C:1088H         LINE#         81
  C:1089H         LINE#         82
  C:108BH         LINE#         83
  C:108CH         LINE#         84
  C:1090H         LINE#         85
  C:1091H         LINE#         86
  C:1093H         LINE#         87
  C:1093H         LINE#         88
  C:1098H         LINE#         89
  C:10A4H         LINE#         90
  C:10A6H         LINE#         91
  C:10A7H         LINE#         92
  C:10A9H         LINE#         93
  C:10AAH         LINE#         94
  C:10AEH         LINE#         96
  C:10B0H         LINE#         97
  C:10B1H         LINE#         98
  C:10B3H         LINE#         99
  C:10B4H         LINE#         100
  C:10B6H         LINE#         101
  C:10B7H         LINE#         102
  C:10B9H         LINE#         103
  C:10BAH         LINE#         104
  C:10BCH         LINE#         105
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:11DBH         LINE#         114
  C:11DBH         LINE#         115
  C:11DBH         LINE#         117
  C:11E2H         LINE#         118
  C:11E4H         LINE#         119
  C:11E4H         LINE#         120
  C:11F3H         LINE#         121
  C:11F7H         LINE#         122
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:12CEH         LINE#         132
  C:12CEH         LINE#         133
  C:12CEH         LINE#         135
  C:12D0H         LINE#         136
  C:12D0H         LINE#         137
  C:12DFH         LINE#         138
  C:12E3H         LINE#         140
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 15



  -------         MODULE        UART3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  B:00A8H.4       PUBLIC        ES
  D:00B8H         PUBLIC        IP
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  C:12E4H         PUBLIC        InitUart
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          INITUART
  C:12E4H         LINE#         6
  C:12E4H         LINE#         7
  C:12E4H         LINE#         8
  C:12E7H         LINE#         9
  C:12EDH         LINE#         10
  C:12F0H         LINE#         11
  C:12F3H         LINE#         12
  C:12F9H         LINE#         13
  -------         ENDPROC       INITUART
  -------         ENDMOD        UART3085

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:118EH         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:0B6FH         PUBLIC        LCD9648_Init_Proc
  C:12B5H         PUBLIC        main
  C:12FAH         PUBLIC        Timer0_Init
  D:0008H         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:0009H         PUBLIC        value_temp
  D:000DH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  D:008CH         PUBLIC        TH0
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:00C8H         PUBLIC        T2CON
  D:000FH         PUBLIC        disp_buff1
  D:001DH         PUBLIC        Time_Flag
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 16


  D:00D0H         PUBLIC        PSW
  -------         PROC          TIMER0_INIT
  C:12FAH         LINE#         19
  C:12FAH         LINE#         20
  C:12FAH         LINE#         21
  C:12FDH         LINE#         22
  C:1300H         LINE#         23
  C:1303H         LINE#         24
  C:1306H         LINE#         25
  C:1308H         LINE#         26
  C:130AH         LINE#         27
  C:130CH         LINE#         28
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:118EH         LINE#         34
  C:1192H         LINE#         36
  C:1195H         LINE#         37
  C:1198H         LINE#         39
  C:11A0H         LINE#         41
  C:11A9H         LINE#         42
  C:11A9H         LINE#         43
  C:11ACH         LINE#         44
  C:11B2H         LINE#         46
  C:11B2H         LINE#         47
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0B6FH         LINE#         48
  C:0B6FH         LINE#         49
  C:0B6FH         LINE#         50
  C:0B7BH         LINE#         51
  C:0B7BH         LINE#         52
  C:0B7BH         LINE#         53
  C:0B7EH         LINE#         54
  C:0B81H         LINE#         56
  C:0B8DH         LINE#         57
  C:0B9AH         LINE#         59
  C:0BA7H         LINE#         60
  C:0BB4H         LINE#         61
  C:0BC1H         LINE#         62
  C:0BCEH         LINE#         63
  C:0BD8H         LINE#         64
  C:0BDBH         LINE#         66
  C:0BDBH         LINE#         67
  C:0BDEH         LINE#         68
  C:0BE1H         LINE#         70
  C:0BEDH         LINE#         71
  C:0BFAH         LINE#         72
  C:0C07H         LINE#         73
  C:0C14H         LINE#         74
  C:0C21H         LINE#         75
  C:0C2EH         LINE#         77
  C:0C3BH         LINE#         78
  C:0C48H         LINE#         79
  C:0C55H         LINE#         80
  C:0C62H         LINE#         81
  C:0C6FH         LINE#         82
  C:0C7CH         LINE#         84
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 17


  C:0C89H         LINE#         85
  C:0C96H         LINE#         86
  C:0CA3H         LINE#         87
  C:0CB0H         LINE#         88
  C:0CB0H         LINE#         90
  C:0CB0H         LINE#         92
  C:0CB0H         LINE#         93
  C:0CB0H         LINE#         97
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          MAIN
  C:12B5H         LINE#         99
  C:12B5H         LINE#         100
  C:12B5H         LINE#         101
  C:12B8H         LINE#         102
  C:12BBH         LINE#         103
  C:12BFH         LINE#         104
  C:12BFH         LINE#         105
  C:12C2H         LINE#         106
  C:12C9H         LINE#         107
  C:12CCH         LINE#         108
  C:12CCH         LINE#         109
  C:12CCH         LINE#         110
  C:12CCH         LINE#         112
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        ?C?FPMUL
  C:098DH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:0A9DH         PUBLIC        ?C?FCASTC
  C:0A98H         PUBLIC        ?C?FCASTI
  C:0A93H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0B1BH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0B34H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0B61H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?SPI_START?ADC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_READ_AD_DATA?ADC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:52:48  PAGE 18


*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS1302INIT?RTC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS1302READTIME?RTC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?INITUART?UART3085

Program Size: data=49.0 xdata=0 code=4954
LINK/LOCATE RUN COMPLETE.  8 WARNING(S),  0 ERROR(S)
