BL51 BANKED LINKER/LOCATER V6.22                                                        07/07/2025  16:24:20  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\adc3085.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Obje
>> cts\rtc3085.obj, .\Objects\uart3085.obj TO .\Objects\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\adc3085.obj (ADC3085)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\rtc3085.obj (RTC3085)
  .\Objects\uart3085.obj (UART3085)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0016H     UNIT         ?DT?MAIN
            DATA    001EH     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    0024H     0006H     UNIT         ?DT?RTC3085
            DATA    002AH     0004H     UNIT         _DATA_GROUP_
            DATA    002EH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   0030H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     097FH     UNIT         ?CO?LCD9648
            CODE    098DH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0B6FH     0142H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0CB1H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0D74H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    0E20H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    0EAEH     008CH     UNIT         ?C_C51STARTUP
            CODE    0F3AH     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    0FABH     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    1013H     004FH     UNIT         ?CO?MAIN
            CODE    1062H     004AH     UNIT         ?PR?_DS1302READ?RTC3085
            CODE    10ACH     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    10E7H     0035H     UNIT         ?PR?_DS1302WRITE?RTC3085
            CODE    111CH     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 2


            CODE    1150H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    117DH     0029H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    11A6H     0024H     UNIT         ?PR?SPI_READ?ADC3085
            CODE    11CAH     0023H     UNIT         ?PR?DS1302INIT?RTC3085
            CODE    11EDH     001CH     UNIT         ?PR?_SPI_WRITE?ADC3085
            CODE    1209H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1224H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    123FH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1259H     0019H     UNIT         ?PR?MAIN?MAIN
            CODE    1272H     0019H     UNIT         ?C_INITSEG
            CODE    128BH     0019H     UNIT         ?PR?_READ_AD_DATA?ADC3085
            CODE    12A4H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    12BDH     0016H     UNIT         ?PR?DS1302READTIME?RTC3085
            CODE    12D3H     0016H     UNIT         ?PR?INITUART?UART3085
            CODE    12E9H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    12FCH     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    130EH     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    131DH     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    132BH     000CH     UNIT         ?CO?RTC3085
            CODE    1337H     000BH     UNIT         ?PR?SPI_START?ADC3085
            CODE    1342H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 3



?PR?_LCD9648_WRITE16CNCHAR?LCD9648          002AH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          002AH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:117DH         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:0B6FH         PUBLIC        LCD9648_Init_Proc
  C:1259H         PUBLIC        main
  C:12E9H         PUBLIC        Timer0_Init
  D:0008H         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:0009H         PUBLIC        value_temp
  D:000DH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  D:008CH         PUBLIC        TH0
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:00C8H         PUBLIC        T2CON
  D:000FH         PUBLIC        disp_buff1
  D:001DH         PUBLIC        Time_Flag
  D:00D0H         PUBLIC        PSW
  -------         PROC          TIMER0_INIT
  C:12E9H         LINE#         19
  C:12E9H         LINE#         20
  C:12E9H         LINE#         21
  C:12ECH         LINE#         22
  C:12EFH         LINE#         23
  C:12F2H         LINE#         24
  C:12F5H         LINE#         25
  C:12F7H         LINE#         26
  C:12F9H         LINE#         27
  C:12FBH         LINE#         28
  -------         ENDPROC       TIMER0_INIT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 4


  -------         PROC          TIMER0_ISR
  C:117DH         LINE#         34
  C:1181H         LINE#         36
  C:1184H         LINE#         37
  C:1187H         LINE#         39
  C:118FH         LINE#         41
  C:1198H         LINE#         42
  C:1198H         LINE#         43
  C:119BH         LINE#         44
  C:11A1H         LINE#         46
  C:11A1H         LINE#         47
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0B6FH         LINE#         48
  C:0B6FH         LINE#         49
  C:0B6FH         LINE#         50
  C:0B7BH         LINE#         51
  C:0B7BH         LINE#         52
  C:0B7BH         LINE#         53
  C:0B7EH         LINE#         54
  C:0B81H         LINE#         56
  C:0B8DH         LINE#         57
  C:0B9AH         LINE#         59
  C:0BA7H         LINE#         60
  C:0BB4H         LINE#         61
  C:0BC1H         LINE#         62
  C:0BCEH         LINE#         63
  C:0BD8H         LINE#         64
  C:0BDBH         LINE#         66
  C:0BDBH         LINE#         67
  C:0BDEH         LINE#         68
  C:0BE1H         LINE#         70
  C:0BEDH         LINE#         71
  C:0BFAH         LINE#         72
  C:0C07H         LINE#         73
  C:0C14H         LINE#         74
  C:0C21H         LINE#         75
  C:0C2EH         LINE#         77
  C:0C3BH         LINE#         78
  C:0C48H         LINE#         79
  C:0C55H         LINE#         80
  C:0C62H         LINE#         81
  C:0C6FH         LINE#         82
  C:0C7CH         LINE#         84
  C:0C89H         LINE#         85
  C:0C96H         LINE#         86
  C:0CA3H         LINE#         87
  C:0CB0H         LINE#         88
  C:0CB0H         LINE#         90
  C:0CB0H         LINE#         92
  C:0CB0H         LINE#         93
  C:0CB0H         LINE#         97
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          MAIN
  C:1259H         LINE#         99
  C:1259H         LINE#         100
  C:1259H         LINE#         101
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 5


  C:125CH         LINE#         102
  C:125FH         LINE#         103
  C:1263H         LINE#         104
  C:1263H         LINE#         105
  C:1266H         LINE#         106
  C:126DH         LINE#         107
  C:1270H         LINE#         108
  C:1270H         LINE#         109
  C:1270H         LINE#         110
  C:1270H         LINE#         112
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        ADC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  B:00A0H.5       PUBLIC        XPT2046_DIN
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:128BH         PUBLIC        _Read_AD_Data
  B:00A0H.6       PUBLIC        XPT2046_CS
  C:11A6H         PUBLIC        SPI_Read
  B:00A0H.7       PUBLIC        XPT2046_SCLK
  C:11EDH         PUBLIC        _SPI_Write
  C:1337H         PUBLIC        SPI_Start
  D:00C8H         PUBLIC        T2CON
  B:00A0H.4       PUBLIC        XPT2046_DOUT
  D:00D0H         PUBLIC        PSW
  -------         PROC          SPI_START
  C:1337H         LINE#         9
  C:1337H         LINE#         10
  C:1337H         LINE#         11
  C:1339H         LINE#         12
  C:133BH         LINE#         13
  C:133DH         LINE#         14
  C:133FH         LINE#         15
  C:1341H         LINE#         16
  -------         ENDPROC       SPI_START
  -------         PROC          _SPI_WRITE
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:11EDH         LINE#         24
  C:11EDH         LINE#         25
  C:11EDH         LINE#         27
  C:11EFH         LINE#         28
  C:11F1H         LINE#         29
  C:11F1H         LINE#         30
  C:11FCH         LINE#         31
  C:1200H         LINE#         32
  C:1202H         LINE#         34
  C:1204H         LINE#         36
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 6


  C:1208H         LINE#         37
  -------         ENDPROC       _SPI_WRITE
  -------         PROC          SPI_READ
  -------         DO            
  D:0004H         SYMBOL        i
  D:0006H         SYMBOL        dat
  -------         ENDDO         
  C:11A6H         LINE#         45
  C:11A6H         LINE#         46
  C:11A6H         LINE#         47
  C:11A9H         LINE#         48
  C:11ABH         LINE#         49
  C:11ADH         LINE#         50
  C:11ADH         LINE#         51
  C:11B4H         LINE#         53
  C:11B6H         LINE#         54
  C:11B8H         LINE#         56
  C:11BEH         LINE#         58
  C:11C9H         LINE#         59
  C:11C9H         LINE#         60
  -------         ENDPROC       SPI_READ
  -------         PROC          _READ_AD_DATA
  D:0007H         SYMBOL        cmd
  -------         DO            
  D:0007H         SYMBOL        i
  D:0006H         SYMBOL        AD_Value
  -------         ENDDO         
  C:128BH         LINE#         68
  C:128BH         LINE#         69
  C:128BH         LINE#         72
  C:128DH         LINE#         73
  C:128FH         LINE#         74
  C:1292H         LINE#         75
  C:1296H         LINE#         76
  C:1298H         LINE#         77
  C:1299H         LINE#         78
  C:129AH         LINE#         79
  C:129CH         LINE#         80
  C:129DH         LINE#         81
  C:129EH         LINE#         82
  C:12A1H         LINE#         83
  C:12A3H         LINE#         84
  C:12A3H         LINE#         85
  -------         ENDPROC       _READ_AD_DATA
  -------         ENDMOD        ADC3085

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:131DH         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1209H         PUBLIC        ds18b20_read_byte
  C:1342H         PUBLIC        ds18b20_init
  C:123FH         PUBLIC        ds18b20_read_bit
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 7


  C:1124H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:0F3AH         PUBLIC        ds18b20_read_temperture
  C:130EH         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:10ACH         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:130EH         LINE#         4
  C:130EH         LINE#         5
  C:130EH         LINE#         7
  C:1314H         LINE#         8
  C:1314H         LINE#         9
  C:1316H         LINE#         10
  C:131CH         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:131DH         LINE#         21
  C:131DH         LINE#         22
  C:131DH         LINE#         23
  C:131FH         LINE#         24
  C:1324H         LINE#         25
  C:1326H         LINE#         26
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:10ACH         LINE#         35
  C:10ACH         LINE#         36
  C:10ACH         LINE#         37
  C:10AEH         LINE#         39
  C:10B7H         LINE#         40
  C:10B7H         LINE#         41
  C:10B8H         LINE#         42
  C:10BDH         LINE#         43
  C:10BFH         LINE#         44
  C:10C8H         LINE#         45
  C:10CAH         LINE#         46
  C:10D3H         LINE#         47
  C:10D3H         LINE#         48
  C:10D4H         LINE#         49
  C:10D9H         LINE#         50
  C:10DBH         LINE#         51
  C:10E4H         LINE#         52
  C:10E6H         LINE#         53
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 8


  C:123FH         LINE#         61
  C:123FH         LINE#         62
  C:123FH         LINE#         63
  C:1241H         LINE#         65
  C:1243H         LINE#         66
  C:1245H         LINE#         67
  C:1247H         LINE#         68
  C:1249H         LINE#         69
  C:124FH         LINE#         70
  C:1251H         LINE#         71
  C:1256H         LINE#         72
  C:1258H         LINE#         73
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1209H         LINE#         81
  C:1209H         LINE#         82
  C:1209H         LINE#         83
  C:120BH         LINE#         84
  C:120CH         LINE#         85
  C:120DH         LINE#         87
  C:120DH         LINE#         88
  C:120DH         LINE#         89
  C:1210H         LINE#         90
  C:121DH         LINE#         91
  C:1221H         LINE#         92
  C:1223H         LINE#         93
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1124H         LINE#         101
  C:1126H         LINE#         102
  C:1126H         LINE#         103
  C:1128H         LINE#         104
  C:1128H         LINE#         106
  C:1128H         LINE#         107
  C:1128H         LINE#         108
  C:112CH         LINE#         109
  C:1130H         LINE#         110
  C:1133H         LINE#         111
  C:1133H         LINE#         112
  C:1135H         LINE#         113
  C:1137H         LINE#         114
  C:1139H         LINE#         115
  C:113EH         LINE#         116
  C:1140H         LINE#         118
  C:1140H         LINE#         119
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 9


  C:1142H         LINE#         120
  C:1147H         LINE#         121
  C:1149H         LINE#         122
  C:114BH         LINE#         123
  C:114BH         LINE#         124
  C:114FH         LINE#         125
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0003H         LINE#         137
  C:0006H         LINE#         138
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1342H         LINE#         147
  C:1342H         LINE#         148
  C:1342H         LINE#         149
  C:1345H         LINE#         150
  C:1348H         LINE#         151
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:001EH         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:0022H         SYMBOL        value
  -------         ENDDO         
  C:0F3AH         LINE#         159
  C:0F3AH         LINE#         160
  C:0F3AH         LINE#         162
  C:0F3CH         LINE#         163
  C:0F3DH         LINE#         164
  C:0F41H         LINE#         166
  C:0F44H         LINE#         167
  C:0F44H         LINE#         168
  C:0F44H         LINE#         169
  C:0F47H         LINE#         170
  C:0F4CH         LINE#         172
  C:0F51H         LINE#         173
  C:0F54H         LINE#         174
  C:0F60H         LINE#         176
  C:0F67H         LINE#         177
  C:0F67H         LINE#         178
  C:0F78H         LINE#         179
  C:0F86H         LINE#         180
  C:0F88H         LINE#         182
  C:0F88H         LINE#         183
  C:0FA2H         LINE#         184
  C:0FA2H         LINE#         185
  C:0FAAH         LINE#         186
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 10


  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1235H         PUBLIC        _WriteData
  C:0E20H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1304H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:0FABH         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:033EH         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0CB1H         PUBLIC        _LCD9648_Write16CnCHAR
  C:0D74H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:12A4H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:1150H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:12A4H         LINE#         4
  C:12A4H         LINE#         5
  C:12A4H         LINE#         8
  C:12A6H         LINE#         9
  C:12A6H         LINE#         10
  C:12AEH         LINE#         11
  C:12B0H         LINE#         13
  C:12B4H         LINE#         15
  C:12B6H         LINE#         16
  C:12B8H         LINE#         17
  C:12BCH         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:12FEH         SYMBOL        L?0067
  C:1300H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:12FEH         SYMBOL        L?0067
  C:1300H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1304H         LINE#         20
  C:1304H         LINE#         21
  C:1304H         LINE#         23
  C:1306H         LINE#         24
  C:1308H         LINE#         26
  C:130BH         LINE#         28
  C:130DH         LINE#         30
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 11


  -------         ENDPROC       _WRITECOMM
  C:1229H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:1229H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:1235H         LINE#         31
  C:1235H         LINE#         32
  C:1235H         LINE#         33
  C:1237H         LINE#         34
  C:1239H         LINE#         36
  C:123CH         LINE#         38
  C:123EH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:0FABH         LINE#         41
  C:0FABH         LINE#         42
  C:0FABH         LINE#         46
  C:0FADH         LINE#         47
  C:0FBBH         LINE#         49
  C:0FBDH         LINE#         50
  C:0FCBH         LINE#         52
  C:0FCDH         LINE#         53
  C:0FDBH         LINE#         55
  C:0FE2H         LINE#         56
  C:0FE9H         LINE#         57
  C:0FF0H         LINE#         58
  C:0FF7H         LINE#         59
  C:0FFEH         LINE#         60
  C:1005H         LINE#         61
  C:100CH         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1150H         LINE#         67
  C:1150H         LINE#         68
  C:1150H         LINE#         71
  C:1152H         LINE#         72
  C:1152H         LINE#         73
  C:1159H         LINE#         74
  C:115FH         LINE#         75
  C:1166H         LINE#         76
  C:116CH         LINE#         78
  C:116EH         LINE#         79
  C:116EH         LINE#         80
  C:1174H         LINE#         81
  C:1178H         LINE#         82
  C:117CH         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 12


  D:002AH         SYMBOL        x
  D:002BH         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:002CH         SYMBOL        x1
  D:002DH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0CB1H         LINE#         86
  C:0CB5H         LINE#         87
  C:0CB5H         LINE#         91
  C:0CBCH         LINE#         92
  C:0CBCH         LINE#         93
  C:0CBFH         LINE#         94
  C:0CBFH         LINE#         97
  C:0CC6H         LINE#         98
  C:0CC6H         LINE#         99
  C:0CC9H         LINE#         100
  C:0CC9H         LINE#         101
  C:0CCFH         LINE#         103
  C:0CD5H         LINE#         104
  C:0CDDH         LINE#         105
  C:0CDDH         LINE#         108
  C:0CE4H         LINE#         110
  C:0CEBH         LINE#         111
  C:0CF1H         LINE#         113
  C:0CF4H         LINE#         114
  C:0CFBH         LINE#         115
  C:0CFDH         LINE#         116
  C:0CFDH         LINE#         118
  C:0D30H         LINE#         120
  C:0D30H         LINE#         121
  C:0D31H         LINE#         122
  C:0D31H         LINE#         123
  C:0D36H         LINE#         124
  C:0D36H         LINE#         126
  C:0D3DH         LINE#         129
  C:0D40H         LINE#         130
  C:0D47H         LINE#         131
  C:0D47H         LINE#         132
  C:0D57H         LINE#         133
  C:0D5BH         LINE#         134
  C:0D61H         LINE#         135
  C:0D61H         LINE#         136
  C:0D67H         LINE#         137
  C:0D6EH         LINE#         139
  C:0D71H         LINE#         140
  C:0D73H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:002AH         SYMBOL        x
  D:002BH         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:002CH         SYMBOL        x1
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 13


  D:002DH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0D74H         LINE#         143
  C:0D78H         LINE#         144
  C:0D78H         LINE#         148
  C:0D7FH         LINE#         149
  C:0D7FH         LINE#         150
  C:0D82H         LINE#         151
  C:0D82H         LINE#         154
  C:0D89H         LINE#         155
  C:0D89H         LINE#         156
  C:0D8CH         LINE#         157
  C:0D8CH         LINE#         158
  C:0D92H         LINE#         160
  C:0D98H         LINE#         161
  C:0DA0H         LINE#         162
  C:0DA0H         LINE#         165
  C:0DA7H         LINE#         167
  C:0DAEH         LINE#         168
  C:0DB4H         LINE#         170
  C:0DB9H         LINE#         171
  C:0DC0H         LINE#         172
  C:0DC2H         LINE#         173
  C:0DC2H         LINE#         175
  C:0DDAH         LINE#         176
  C:0DDAH         LINE#         177
  C:0DDBH         LINE#         178
  C:0DDBH         LINE#         179
  C:0DE0H         LINE#         180
  C:0DE0H         LINE#         182
  C:0DE7H         LINE#         185
  C:0DECH         LINE#         186
  C:0DF3H         LINE#         187
  C:0DF3H         LINE#         188
  C:0E03H         LINE#         189
  C:0E07H         LINE#         190
  C:0E0DH         LINE#         191
  C:0E0DH         LINE#         192
  C:0E13H         LINE#         193
  C:0E1AH         LINE#         195
  C:0E1DH         LINE#         196
  C:0E1FH         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:002EH         SYMBOL        x1
  D:002FH         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E20H         LINE#         198
  C:0E22H         LINE#         199
  C:0E22H         LINE#         203
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 14


  C:0E28H         LINE#         204
  C:0E28H         LINE#         205
  C:0E2BH         LINE#         206
  C:0E2BH         LINE#         209
  C:0E31H         LINE#         210
  C:0E31H         LINE#         211
  C:0E34H         LINE#         212
  C:0E34H         LINE#         213
  C:0E38H         LINE#         215
  C:0E3EH         LINE#         217
  C:0E3EH         LINE#         220
  C:0E45H         LINE#         222
  C:0E4BH         LINE#         223
  C:0E50H         LINE#         225
  C:0E55H         LINE#         226
  C:0E5CH         LINE#         227
  C:0E5EH         LINE#         228
  C:0E5EH         LINE#         230
  C:0E72H         LINE#         231
  C:0E72H         LINE#         232
  C:0E73H         LINE#         233
  C:0E73H         LINE#         234
  C:0E78H         LINE#         235
  C:0E78H         LINE#         237
  C:0E7EH         LINE#         240
  C:0E83H         LINE#         241
  C:0E8AH         LINE#         242
  C:0E8AH         LINE#         243
  C:0E9FH         LINE#         244
  C:0EA3H         LINE#         245
  C:0EA7H         LINE#         246
  C:0EA7H         LINE#         247
  C:0EABH         LINE#         248
  C:0EABH         LINE#         250
  C:0EABH         LINE#         251
  C:0EADH         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        RTC3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  C:10E7H         PUBLIC        _Ds1302Write
  C:12BDH         PUBLIC        Ds1302ReadTime
  C:11CAH         PUBLIC        Ds1302Init
  D:00B8H         PUBLIC        IP
  B:00B0H.6       PUBLIC        SCLK
  B:00B0H.4       PUBLIC        DSIO
  D:0024H         PUBLIC        TIME
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1331H         PUBLIC        WRITE_RTC_ADDR
  C:132BH         PUBLIC        READ_RTC_ADDR
  D:00C8H         PUBLIC        T2CON
  B:00B0H.5       PUBLIC        RST
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 15


  D:00D0H         PUBLIC        PSW
  C:1062H         PUBLIC        _Ds1302Read
  -------         PROC          _DS1302WRITE
  D:0007H         SYMBOL        addr
  D:0005H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        n
  -------         ENDDO         
  C:10E7H         LINE#         24
  C:10E7H         LINE#         25
  C:10E7H         LINE#         27
  C:10E9H         LINE#         28
  C:10EAH         LINE#         30
  C:10ECH         LINE#         31
  C:10EDH         LINE#         32
  C:10EFH         LINE#         33
  C:10F0H         LINE#         35
  C:10F2H         LINE#         36
  C:10F2H         LINE#         37
  C:10F6H         LINE#         38
  C:10FAH         LINE#         39
  C:10FCH         LINE#         40
  C:10FDH         LINE#         41
  C:10FFH         LINE#         42
  C:1100H         LINE#         43
  C:1104H         LINE#         44
  C:1106H         LINE#         45
  C:1106H         LINE#         46
  C:110AH         LINE#         47
  C:110EH         LINE#         48
  C:1110H         LINE#         49
  C:1111H         LINE#         50
  C:1113H         LINE#         51
  C:1114H         LINE#         52
  C:1118H         LINE#         54
  C:111AH         LINE#         55
  C:111BH         LINE#         56
  -------         ENDPROC       _DS1302WRITE
  -------         PROC          _DS1302READ
  D:0007H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        n
  D:0006H         SYMBOL        dat
  D:0007H         SYMBOL        dat1
  -------         ENDDO         
  C:1062H         LINE#         65
  C:1062H         LINE#         66
  C:1062H         LINE#         68
  C:1064H         LINE#         69
  C:1065H         LINE#         71
  C:1067H         LINE#         72
  C:1068H         LINE#         73
  C:106AH         LINE#         74
  C:106BH         LINE#         76
  C:106DH         LINE#         77
  C:106DH         LINE#         78
  C:1071H         LINE#         79
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 16


  C:1075H         LINE#         80
  C:1077H         LINE#         81
  C:1078H         LINE#         82
  C:107AH         LINE#         83
  C:107BH         LINE#         84
  C:107FH         LINE#         85
  C:1080H         LINE#         86
  C:1082H         LINE#         87
  C:1082H         LINE#         88
  C:1087H         LINE#         89
  C:1093H         LINE#         90
  C:1095H         LINE#         91
  C:1096H         LINE#         92
  C:1098H         LINE#         93
  C:1099H         LINE#         94
  C:109DH         LINE#         96
  C:109FH         LINE#         97
  C:10A0H         LINE#         98
  C:10A2H         LINE#         99
  C:10A3H         LINE#         100
  C:10A5H         LINE#         101
  C:10A6H         LINE#         102
  C:10A8H         LINE#         103
  C:10A9H         LINE#         104
  C:10ABH         LINE#         105
  -------         ENDPROC       _DS1302READ
  -------         PROC          DS1302INIT
  -------         DO            
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:11CAH         LINE#         114
  C:11CAH         LINE#         115
  C:11CAH         LINE#         117
  C:11D1H         LINE#         118
  C:11D3H         LINE#         119
  C:11D3H         LINE#         120
  C:11E2H         LINE#         121
  C:11E6H         LINE#         122
  -------         ENDPROC       DS1302INIT
  -------         PROC          DS1302READTIME
  -------         DO            
  D:0003H         SYMBOL        n
  -------         ENDDO         
  C:12BDH         LINE#         132
  C:12BDH         LINE#         133
  C:12BDH         LINE#         135
  C:12BFH         LINE#         136
  C:12BFH         LINE#         137
  C:12CEH         LINE#         138
  C:12D2H         LINE#         140
  -------         ENDPROC       DS1302READTIME
  -------         ENDMOD        RTC3085

  -------         MODULE        UART3085
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 17


  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  B:00A8H.4       PUBLIC        ES
  D:00B8H         PUBLIC        IP
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  C:12D3H         PUBLIC        InitUart
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          INITUART
  C:12D3H         LINE#         6
  C:12D3H         LINE#         7
  C:12D3H         LINE#         8
  C:12D6H         LINE#         9
  C:12DCH         LINE#         10
  C:12DFH         LINE#         11
  C:12E2H         LINE#         12
  C:12E8H         LINE#         13
  -------         ENDPROC       INITUART
  -------         ENDMOD        UART3085

  -------         MODULE        ?C?FPMUL
  C:098DH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:0A9DH         PUBLIC        ?C?FCASTC
  C:0A98H         PUBLIC        ?C?FCASTI
  C:0A93H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0B1BH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0B34H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0B61H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?SPI_START?ADC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_READ_AD_DATA?ADC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  16:24:20  PAGE 18


*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS1302INIT?RTC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS1302READTIME?RTC3085

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?INITUART?UART3085

Program Size: data=49.0 xdata=0 code=4937
LINK/LOCATE RUN COMPLETE.  8 WARNING(S),  0 ERROR(S)
