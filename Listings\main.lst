C51 COMPILER V9.54   MAIN                                                                  07/07/2025 17:14:35 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE MAIN
OBJECT MODULE PLACED IN .\Objects\main.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE main.c OPTIMIZE(8,SPEED) BROWSE DEBUG OBJECTEXTEND PRINT(.\Listings\main.ls
                    -t) OBJECT(.\Objects\main.obj)

line level    source

   1          #include <reg52.h>
   2          //#include <adc3085.h>
   3          #include <lcd9648.h>
   4          //#include <uart3085.h>
   5          
   6          unsigned int Timer0_count = 0;    // 定时器计数器
   7          unsigned char Time_Flag = 0;     // 6秒到达标志
   8          
   9          unsigned char LCD_Init_Mode = 1; //模式1：显示姓名学号班级 模式2：显示功能介绍 模式3
             -：正常显示
  10          /*-----------------------------------------------------------------------------
  11           * 函数名称: Timer0_Init
  12           * 函数功能: 定时器0初始化，1ms中断一次
  13           *----------------------------------------------------------------------------*/
  14          void Timer0_Init(void)
  15          {
  16   1          TMOD &= 0xF0;    // 清除定时器0模式位
  17   1          TMOD |= 0x01;    // 定时器0工作在模式1，16位定时器
  18   1          TH0 = 0xFC;      // 1ms@11.0592MHz
  19   1          TL0 = 0x66;
  20   1          ET0 = 1;         // 使能定时器0中断
  21   1          EA = 1;          // 使能总中断
  22   1          TR0 = 1;         // 启动定时器0
  23   1      }
  24          
  25          /*-----------------------------------------------------------------------------
  26           * 函数名称: Timer0_ISR
  27           * 函数功能: 定时器0中断服务函数
  28           *----------------------------------------------------------------------------*/
  29          void Timer0_ISR(void) interrupt 1
  30          {
  31   1          TH0 = 0xFC;      // 重装载值
  32   1          TL0 = 0x66;
  33   1          
  34   1          Timer0_count++;   // 计数器递增
  35   1          
  36   1          if(Timer0_count >= 6000)  // 6000ms = 6秒
  37   1          {
  38   2              Time_Flag = 1;       // 设置6秒到达标志
  39   2              Timer0_count = 0;     // 重置计数器
  40   2      
  41   2          }
  42   1      }
  43          void LCD9648_Init_Proc()
  44          {
  45   1              switch(LCD_Init_Mode)
  46   1              {
  47   2                      case 1:
  48   2                              LCD9648_Init();
  49   2                              LCD9648_Clear();
  50   2                              
  51   2                              LCD9648_Write16CnCHAR(0,0,"李欣蓬");
  52   2                              LCD9648_Write16EnCHAR(0,2,"202303103085");
  53   2                              
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 17:14:35 PAGE 2   

  54   2                              LCD9648_Write16CnCHAR(0,4,"电");
  55   2                              LCD9648_Write16CnCHAR(16,4,"气");
  56   2                              LCD9648_Write16EnCHAR(32,4,"23");
  57   2                              LCD9648_Write16EnCHAR(48,4,"4");
  58   2                              LCD9648_Write16CnCHAR(56,4,"班");
  59   2                      break;
  60   2                      
  61   2                      case 2:
  62   2                              LCD9648_Init();
  63   2                              LCD9648_Clear();
  64   2                              
  65   2                              LCD9648_Write16CnCHAR(0,0,"便");
  66   2                              LCD9648_Write16CnCHAR(16,0,"携");
  67   2                              LCD9648_Write16CnCHAR(32,0,"式");
  68   2                              LCD9648_Write16CnCHAR(48,0,"保");
  69   2                              LCD9648_Write16CnCHAR(64,0,"温");
  70   2                              LCD9648_Write16CnCHAR(80,0,"杯");
  71   2                              
  72   2                              LCD9648_Write16EnCHAR(0,2,"25");//x=0,y=4,年字符
  73   2                              LCD9648_Write16CnCHAR(16,2,"年");
  74   2                              LCD9648_Write16EnCHAR(32,2,"07");
  75   2                              LCD9648_Write16CnCHAR(48,2,"月");
  76   2                              LCD9648_Write16EnCHAR(64,2,"11");
  77   2                              LCD9648_Write16CnCHAR(80,2,"日");
  78   2                      
  79   2                              LCD9648_Write16CnCHAR(0,4,"版");
  80   2                              LCD9648_Write16CnCHAR(16,4,"本");
  81   2                              LCD9648_Write16EnCHAR(32,4,"1.");
  82   2                              LCD9648_Write16CnCHAR(48,4,"23");
  83   2                      break;
  84   2                      
  85   2                      case 3: //正常显示模式
  86   2                  LCD9648_Init();
  87   2                  LCD9648_Clear();
  88   2                  LCD9648_Write16CnCHAR(0,0,"正常工作模式");
  89   2                  LCD9648_Write16CnCHAR(0,2,"温度监测中...");
  90   2              break;
  91   2                      
  92   2                      default:
  93   2                              
  94   2                      break;
  95   2              }
  96   1              
  97   1      
  98   1              
  99   1      }
 100          
 101          void main()
 102          {
 103   1          LCD9648_Init_Proc(); //显示初始界面
 104   1          Timer0_Init();        //启动定时器
 105   1          
 106   1          while(1)
 107   1          {
 108   2              if(Time_Flag)     //6秒时间到
 109   2              {
 110   3                  Time_Flag = 0;
 111   3                  LCD_Init_Mode++; //切换到下一个模式
 112   3                  if(LCD_Init_Mode <= 3)
 113   3                  {
 114   4                      LCD9648_Init_Proc(); //更新显示
 115   4                  }
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 17:14:35 PAGE 3   

 116   3              }
 117   2          }
 118   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    446    ----
   CONSTANT SIZE    =    134    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =      4    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
