C51 COMPILER V9.54   MAIN                                                                  07/07/2025 16:52:48 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE MAIN
OBJECT MODULE PLACED IN .\Objects\main.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE main.c OPTIMIZE(8,SPEED) BROWSE DEBUG OBJECTEXTEND PRINT(.\Listings\main.ls
                    -t) OBJECT(.\Objects\main.obj)

line level    source

   1          #include <reg52.h>
   2          #include <adc3085.h>
   3          #include <lcd9648.h>
   4          #include <uart3085.h>
   5          
   6          float value_temp = 0;
   7          unsigned char disp_buff1[14];
   8          unsigned char disp_buff1[14];
   9          unsigned char disp_buff1[14];
  10          
  11          unsigned int Timer0_count = 0;    // 定时器计数器
  12          unsigned char Time_Flag = 0;     // 6秒到达标志
  13          
  14          unsigned char LCD_Init_Mode = 0; //模式1：显示姓名学号班级 模式2：显示功能介绍
  15          /*-----------------------------------------------------------------------------
  16           * 函数名称: Timer0_Init
  17           * 函数功能: 定时器0初始化，1ms中断一次
  18           *----------------------------------------------------------------------------*/
  19          void Timer0_Init(void)
  20          {
  21   1          TMOD &= 0xF0;    // 清除定时器0模式位
  22   1          TMOD |= 0x01;    // 定时器0工作在模式1，16位定时器
  23   1          TH0 = 0xFC;      // 1ms@11.0592MHz
  24   1          TL0 = 0x66;
  25   1          ET0 = 1;         // 使能定时器0中断
  26   1          EA = 1;          // 使能总中断
  27   1          TR0 = 1;         // 启动定时器0
  28   1      }
  29          
  30          /*-----------------------------------------------------------------------------
  31           * 函数名称: Timer0_ISR
  32           * 函数功能: 定时器0中断服务函数
  33           *----------------------------------------------------------------------------*/
  34          void Timer0_ISR(void) interrupt 1
  35          {
  36   1          TH0 = 0xFC;      // 重装载值
  37   1          TL0 = 0x66;
  38   1          
  39   1          Timer0_count++;   // 计数器递增
  40   1          
  41   1          if(Timer0_count >= 6000)  // 6000ms = 6秒
  42   1          {
  43   2              Time_Flag = 1;       // 设置6秒到达标志
  44   2              Timer0_count = 0;     // 重置计数器
  45   2      
  46   2          }
  47   1      }
  48          void LCD9648_Init_Proc()
  49          {
  50   1              switch(LCD_Init_Mode)
  51   1              {
  52   2                      case 1:
  53   2                              LCD9648_Init();
  54   2                              LCD9648_Clear();
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 16:52:48 PAGE 2   

  55   2                              
  56   2                              LCD9648_Write16CnCHAR(0,0,"李欣蓬");
  57   2                              LCD9648_Write16EnCHAR(0,2,"202303103085");
  58   2                              
  59   2                              LCD9648_Write16CnCHAR(0,4,"电");
  60   2                              LCD9648_Write16CnCHAR(16,4,"气");
  61   2                              LCD9648_Write16EnCHAR(32,4,"23");
  62   2                              LCD9648_Write16EnCHAR(48,4,"4");
  63   2                              LCD9648_Write16CnCHAR(56,4,"班");
  64   2                      break;
  65   2                      
  66   2                      case 2:
  67   2                              LCD9648_Init();
  68   2                              LCD9648_Clear();
  69   2                              
  70   2                              LCD9648_Write16CnCHAR(0,0,"便");
  71   2                              LCD9648_Write16CnCHAR(16,0,"携");
  72   2                              LCD9648_Write16CnCHAR(32,0,"式");
  73   2                              LCD9648_Write16CnCHAR(48,0,"保");
  74   2                              LCD9648_Write16CnCHAR(64,0,"温");
  75   2                              LCD9648_Write16CnCHAR(80,0,"杯");
  76   2                              
  77   2                              LCD9648_Write16EnCHAR(0,2,"25");//x=0,y=4,年字符
  78   2                              LCD9648_Write16CnCHAR(16,2,"年");
  79   2                              LCD9648_Write16EnCHAR(32,2,"07");
  80   2                              LCD9648_Write16CnCHAR(48,2,"月");
  81   2                              LCD9648_Write16EnCHAR(64,2,"11");
  82   2                              LCD9648_Write16CnCHAR(80,2,"日");
  83   2                      
  84   2                              LCD9648_Write16CnCHAR(0,4,"版");
  85   2                              LCD9648_Write16CnCHAR(16,4,"本");
  86   2                              LCD9648_Write16EnCHAR(32,4,"1.");
  87   2                              LCD9648_Write16CnCHAR(48,4,"23");
  88   2                      break;
  89   2                      
  90   2                      default:
  91   2                              
  92   2                      break;
  93   2              }
  94   1              
  95   1      
  96   1              
  97   1      }
  98          
  99          void main()
 100          {
 101   1              LCD9648_Init_Proc();
 102   1              Timer0_Init();
 103   1              if(Time_Flag)
 104   1              {
 105   2                      Time_Flag = 0;
 106   2                      if(++LCD_Init_Mode == 3)
 107   2                      LCD_Init_Mode = 0;
 108   2              }
 109   1              while(1)
 110   1              {
 111   2                      
 112   2              }
 113   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 16:52:48 PAGE 3   

   CODE SIZE        =    407    ----
   CONSTANT SIZE    =     96    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =     22    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
