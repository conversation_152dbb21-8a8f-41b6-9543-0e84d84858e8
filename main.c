#include <reg52.h>
#include <adc3085.h>
#include <lcd9648.h>
#include <uart3085.h>

float value_temp = 0;
unsigned char disp_buff1[14];
unsigned char disp_buff1[14];
unsigned char disp_buff1[14];

unsigned int Timer0_count = 0;    // 定时器计数器
unsigned char Time_Flag = 0;     // 6秒到达标志

unsigned char LCD_Init_Mode = 0; //模式1：显示姓名学号班级 模式2：显示功能介绍
/*-----------------------------------------------------------------------------
 * 函数名称: Timer0_Init
 * 函数功能: 定时器0初始化，1ms中断一次
 *----------------------------------------------------------------------------*/
void Timer0_Init(void)
{
    TMOD &= 0xF0;    // 清除定时器0模式位
    TMOD |= 0x01;    // 定时器0工作在模式1，16位定时器
    TH0 = 0xFC;      // 1ms@11.0592MHz
    TL0 = 0x66;
    ET0 = 1;         // 使能定时器0中断
    EA = 1;          // 使能总中断
    TR0 = 1;         // 启动定时器0
}

/*-----------------------------------------------------------------------------
 * 函数名称: Timer0_ISR
 * 函数功能: 定时器0中断服务函数
 *----------------------------------------------------------------------------*/
void Timer0_ISR(void) interrupt 1
{
    TH0 = 0xFC;      // 重装载值
    TL0 = 0x66;

    Timer0_count++;   // 计数器递增

    if(Timer0_count >= 6000)  // 6000ms = 6秒
    {
        Time_Flag = 1;       // 设置6秒到达标志
        Timer0_count = 0;     // 重置计数器

    }
}
void LCD9648_Init_Proc()
{
	switch(LCD_Init_Mode)
	{
		case 1:
			LCD9648_Init();
			LCD9648_Clear();

			LCD9648_Write16CnCHAR(0,0,"李鑫鹏");
			LCD9648_Write16EnCHAR(0,2,"202303103085");

			LCD9648_Write16CnCHAR(0,4,"电");
			LCD9648_Write16CnCHAR(16,4,"气");
			LCD9648_Write16EnCHAR(32,4,"23");
			LCD9648_Write16EnCHAR(48,4,"4");
			LCD9648_Write16CnCHAR(56,4,"班");
		break;

		case 2:
			LCD9648_Init();
			LCD9648_Clear();

			LCD9648_Write16CnCHAR(0,0,"便");
			LCD9648_Write16CnCHAR(16,0,"携");
			LCD9648_Write16CnCHAR(32,0,"式");
			LCD9648_Write16CnCHAR(48,0,"保");
			LCD9648_Write16CnCHAR(64,0,"温");
			LCD9648_Write16CnCHAR(80,0,"杯");

			LCD9648_Write16EnCHAR(0,2,"25");//x=0,y=4,年字符
			LCD9648_Write16CnCHAR(16,2,"年");
			LCD9648_Write16EnCHAR(32,2,"07");
			LCD9648_Write16CnCHAR(48,2,"月");
			LCD9648_Write16EnCHAR(64,2,"11");
			LCD9648_Write16CnCHAR(80,2,"日");

			LCD9648_Write16CnCHAR(0,4,"版");
			LCD9648_Write16CnCHAR(16,4,"本");
			LCD9648_Write16EnCHAR(32,4,"1.");
			LCD9648_Write16CnCHAR(48,4,"23");
		break;

		default:

		break;
	}



}

void main()
{
	LCD9648_Init_Proc();
	Timer0_Init();
	if(Time_Flag)
	{
		Time_Flag = 0;
		if(++LCD_Init_Mode == 3)
		LCD_Init_Mode = 0;
	}
	while(1)
	{

	}
}